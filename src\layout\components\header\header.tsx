"use client";
import { use<PERSON><PERSON> } from "jotai";
import { Bell, Heart, LogOut, Search, Settings, ShoppingCart, User } from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "../../../shared/components/shadcn/avatar";
import { Badge } from "../../../shared/components/shadcn/badge";
import { Button } from "../../../shared/components/shadcn/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "../../../shared/components/shadcn/dropdown-menu";
import { Input } from "../../../shared/components/shadcn/input";
import { searchAtom } from "../../atoms/search.atom";
import { MobileMenu } from "./mobile-menu";
import { MobileSearch } from "./mobile-search";

import { Navigation } from "./navigation";
import { TopBar } from "./top-bar";

export const Header = () => {
	const [search, setSearch] = useAtom(searchAtom);
	const [isSearchFocused, setIsSearchFocused] = useState(false);
	const [showSuggestions, setShowSuggestions] = useState(false);
	const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
	const [isMobileSearchOpen, setIsMobileSearchOpen] = useState(false);

	// Mock user data - em um app real, viria de um context/store
	const user = {
		name: "Kevin Damm",
		email: "<EMAIL>",
		avatar: "/api/placeholder/40/40",
		isVip: true,
	};

	const handleSearch = (e: React.FormEvent<HTMLFormElement>) => e.preventDefault();

	// Função para lidar com seleção de sugestão
	const handleSuggestionSelect = (suggestion: string) => {
		setSearch(suggestion);
		setShowSuggestions(false);
		setIsSearchFocused(false);
	};

	// Função para fechar sugestões quando clicar fora
	const handleSearchBlur = () => {
		// Pequeno delay para permitir clique nas sugestões
		setTimeout(() => {
			setShowSuggestions(false);
			setIsSearchFocused(false);
		}, 150);
	};

	return (
		<>
			<header className="bg-white/95 backdrop-blur-md shadow-sm sticky top-0 z-50 border-b border-slate-200/50">
				<TopBar />

				<div className="relative bg-gradient-to-r from-slate-50 via-white to-slate-50/80 border-b border-slate-100/60">
					<div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-amber-200/30 to-transparent"></div>
					<div className="container mx-auto px-4 md:px-6 py-4 md:py-5">
						<div className="flex items-center justify-between gap-4 md:gap-8">
							{/* Logo - mantido o mesmo design sofisticado */}
							<Link href="/" className="flex items-center space-x-3 md:space-x-4 group flex-shrink-0">
								{/* Monograma Premium */}
								<div className="relative">
									{/* Anel externo sutil para destaque */}
									<div className="absolute inset-0 w-12 h-12 md:w-14 md:h-14 rounded-full bg-gradient-to-br from-amber-50 via-yellow-25 to-amber-100 shadow-md transform group-hover:scale-105 transition-transform duration-300 opacity-60"></div>

									{/* Container principal */}
									<div className="relative flex items-center justify-center w-12 h-12 md:w-14 md:h-14 rounded-full bg-gradient-to-br from-slate-900 via-blue-950 to-slate-800 shadow-xl border border-slate-700/20">
										{/* Efeito de brilho sutil */}
										<div className="absolute inset-1 rounded-full bg-gradient-to-br from-white/8 to-transparent"></div>

										{/* Letra K principal */}
										<span
											className="relative z-10 font-serif font-bold text-lg md:text-xl text-white tracking-tighter drop-shadow-sm"
											style={{
												fontFamily: "Playfair Display, Georgia, Times, serif",
												letterSpacing: "-0.05em",
												textShadow: "0 1px 2px rgba(0,0,0,0.3)",
											}}
										>
											K
										</span>

										{/* & Co. em posição elegante */}
										<span
											className="absolute bottom-0.5 right-1 text-xs font-light text-white/90"
											style={{
												fontFamily: "Inter, system-ui, sans-serif",
												letterSpacing: "0.03em",
												fontSize: "0.6rem",
											}}
										>
											&Co.
										</span>

										{/* Linha decorativa minimalista */}
										<div className="absolute bottom-1 left-1/2 w-3 md:w-4 h-px bg-gradient-to-r from-transparent via-white/30 to-transparent transform -translate-x-1/2"></div>
									</div>

									{/* Ponto de luz sutil */}
									<div className="absolute top-2 right-2 w-1 h-1 rounded-full bg-white/50 blur-sm"></div>
								</div>

								{/* Texto da marca */}
								<div className="hidden sm:block space-y-0.5">
									<h1 className="text-2xl md:text-3xl font-light tracking-tight">
										<span
											className="font-serif bg-gradient-to-r from-slate-800 via-slate-700 to-blue-900 bg-clip-text text-transparent"
											style={{
												fontFamily: "Playfair Display, Georgia, Times, serif",
												fontWeight: "400",
											}}
										>
											Kevin
										</span>
										<span
											className="font-extralight text-slate-600 ml-2"
											style={{
												fontFamily: "Inter, system-ui, sans-serif",
												letterSpacing: "0.02em",
											}}
										>
											& Co.
										</span>
									</h1>

									<div className="flex items-center space-x-2">
										<div className="w-6 h-px bg-gradient-to-r from-amber-400 to-yellow-500"></div>
										<p
											className="text-xs text-slate-500 font-medium uppercase tracking-widest"
											style={{
												fontFamily: "Inter, system-ui, sans-serif",
												letterSpacing: "0.15em",
											}}
										>
											Premium E-Commerce
										</p>
									</div>
								</div>

								{/* Versão mobile compacta */}
								<div className="sm:hidden space-y-1">
									<h1 className="text-lg font-light">
										<span
											className="font-serif bg-gradient-to-r from-slate-800 to-blue-900 bg-clip-text text-transparent"
											style={{ fontFamily: "Playfair Display, Georgia, Times, serif" }}
										>
											Kevin
										</span>
										<span className="font-light text-slate-600 ml-1" style={{ fontFamily: "Inter, system-ui, sans-serif" }}>
											& Co.
										</span>
									</h1>
									<p className="text-xs text-slate-500 font-medium tracking-wider">Premium</p>
								</div>
							</Link>

							{/* Barra de pesquisa minimalista - escondida no mobile */}
							<div className="hidden md:flex flex-1 max-w-2xl mx-8">
								<form onSubmit={handleSearch} className="relative w-full">
									<div className="relative">
										<Input
											type="text"
											placeholder="Buscar produtos..."
											value={search || ""}
											onChange={e => setSearch(e.target.value)}
											onFocus={() => {
												setIsSearchFocused(true);
												setShowSuggestions(true);
											}}
											onBlur={handleSearchBlur}
											className={`
												pl-10 pr-4 h-12 w-full
												border border-slate-200 
												${isSearchFocused ? "border-slate-300" : "hover:border-slate-250"} 
												bg-white rounded-lg
												text-slate-700 placeholder:text-slate-400
												transition-all duration-200 ease-out
												focus:ring-1 focus:ring-slate-200
											`}
										/>

										{/* Ícone de pesquisa minimalista */}
										<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
									</div>

									{/* Sugestões minimalistas */}
									{showSuggestions && search && (
										<div className="absolute top-full left-0 right-0 mt-1 bg-white rounded-lg shadow-lg border border-slate-200 z-50 max-h-64 overflow-y-auto">
											<div className="py-2">
												{["iPhone 15 Pro", "MacBook Air M3", "AirPods Pro"].map((suggestion, index) => (
													<button
														key={index}
														className="w-full text-left px-4 py-3 hover:bg-slate-50 text-slate-700 transition-colors duration-150 flex items-center"
														onMouseDown={e => e.preventDefault()} // Previne blur antes do click
														onClick={() => handleSuggestionSelect(suggestion)}
													>
														<Search className="h-4 w-4 mr-3 text-slate-400" />
														{suggestion}
													</button>
												))}
											</div>
										</div>
									)}
								</form>
							</div>

							{/* Área de ações do usuário */}
							<div className="flex items-center space-x-2 md:space-x-4 flex-shrink-0">
								{/* Ações rápidas - mostradas no desktop */}
								<div className="hidden md:flex items-center space-x-4">
									{/* Notificações */}
									<Button
										variant="ghost"
										className="relative p-3 hover:bg-slate-100/80 rounded-xl transition-all duration-300"
										aria-label="Notificações"
									>
										<Bell className="h-5 w-5 text-slate-600 hover:text-amber-600 transition-colors duration-300" />
										<Badge className="absolute -top-1 -right-1 flex items-center justify-center h-5 w-5 p-0 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs shadow-lg border-2 border-white animate-pulse">
											2
										</Badge>
									</Button>

									{/* Favoritos aprimorado */}
									<Button
										variant="ghost"
										className="relative p-3 hover:bg-slate-100/80 rounded-xl transition-all duration-300 group"
										aria-label="Lista de Favoritos"
									>
										<Heart className="h-5 w-5 text-slate-600 group-hover:text-red-500 transition-colors duration-300 group-hover:scale-110 transform" />
										<Badge className="absolute -top-1 -right-1 flex items-center justify-center h-5 w-5 p-0 bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs shadow-lg border-2 border-white">
											3
										</Badge>
									</Button>

									{/* Carrinho aprimorado */}
									<Button
										variant="ghost"
										className="relative p-3 hover:bg-slate-100/80 rounded-xl transition-all duration-300 group"
										aria-label="Carrinho de Compras"
									>
										<ShoppingCart className="h-5 w-5 text-slate-600 group-hover:text-amber-600 transition-colors duration-300 group-hover:scale-110 transform" />
										<Badge className="absolute -top-1 -right-1 flex items-center justify-center h-5 w-5 p-0 bg-gradient-to-r from-slate-800 via-blue-950 to-slate-900 text-white text-xs shadow-lg border-2 border-white">
											2
										</Badge>
									</Button>

									{/* Separador elegante */}
									<div className="h-8 w-px bg-gradient-to-b from-transparent via-slate-300 to-transparent"></div>

									{/* Perfil do usuário premium */}
									<DropdownMenu>
										<DropdownMenuTrigger asChild>
											<Button
												variant="ghost"
												className="relative p-2 hover:bg-slate-100/80 rounded-xl transition-all duration-300 group"
											>
												<div className="flex items-center space-x-3">
													{/* Avatar do usuário */}
													<div className="relative">
														<Avatar className="h-10 w-10 ring-2 ring-amber-200/50 group-hover:ring-amber-300/70 transition-all duration-300">
															<AvatarImage src={user.avatar} alt={user.name} />
															<AvatarFallback className="bg-gradient-to-br from-slate-800 to-blue-900 text-white font-semibold">
																{user.name
																	.split(" ")
																	.map(n => n[0])
																	.join("")}
															</AvatarFallback>
														</Avatar>

														{/* Badge VIP */}
														{user.isVip && (
															<div className="absolute -bottom-1 -right-1 bg-gradient-to-r from-amber-400 to-yellow-500 rounded-full p-1">
																<div className="w-2 h-2 bg-white rounded-full"></div>
															</div>
														)}
													</div>

													{/* Info do usuário (desktop) */}
													<div className="hidden lg:block text-left">
														<div className="text-sm font-medium text-slate-700">{user.name}</div>
														<div className="text-xs text-slate-500 flex items-center">
															{user.isVip && (
																<>
																	<span className="text-amber-600 font-medium">VIP Member</span>
																	<div className="w-1 h-1 bg-amber-500 rounded-full mx-2"></div>
																</>
															)}
															<span>Minha Conta</span>
														</div>
													</div>
												</div>
											</Button>
										</DropdownMenuTrigger>

										<DropdownMenuContent
											className="w-64 mt-2 mr-4 bg-white/95 backdrop-blur-md border border-slate-200/50 shadow-2xl rounded-xl"
											align="end"
										>
											{/* Header do perfil */}
											<div className="px-4 py-3 border-b border-slate-100">
												<div className="flex items-center space-x-3">
													<Avatar className="h-12 w-12">
														<AvatarImage src={user.avatar} alt={user.name} />
														<AvatarFallback className="bg-gradient-to-br from-slate-800 to-blue-900 text-white font-semibold">
															{user.name
																.split(" ")
																.map(n => n[0])
																.join("")}
														</AvatarFallback>
													</Avatar>
													<div>
														<div className="font-medium text-slate-900">{user.name}</div>
														<div className="text-sm text-slate-500">{user.email}</div>
														{user.isVip && (
															<div className="flex items-center mt-1">
																<div className="px-2 py-0.5 bg-gradient-to-r from-amber-100 to-yellow-100 text-amber-700 text-xs font-medium rounded-full">
																	✨ VIP Member
																</div>
															</div>
														)}
													</div>
												</div>
											</div>

											{/* Opções do menu */}
											<div className="py-2">
												<DropdownMenuItem className="px-4 py-3 cursor-pointer hover:bg-slate-50 transition-colors duration-200">
													<User className="mr-3 h-4 w-4 text-slate-500" />
													<span className="text-slate-700">Meu Perfil</span>
												</DropdownMenuItem>

												<DropdownMenuItem className="px-4 py-3 cursor-pointer hover:bg-slate-50 transition-colors duration-200">
													<ShoppingCart className="mr-3 h-4 w-4 text-slate-500" />
													<span className="text-slate-700">Meus Pedidos</span>
												</DropdownMenuItem>

												<DropdownMenuItem className="px-4 py-3 cursor-pointer hover:bg-slate-50 transition-colors duration-200">
													<Heart className="mr-3 h-4 w-4 text-slate-500" />
													<span className="text-slate-700">Lista de Desejos</span>
												</DropdownMenuItem>

												{user.isVip && (
													<DropdownMenuItem className="px-4 py-3 cursor-pointer hover:bg-amber-50 transition-colors duration-200">
														<div className="w-4 h-4 mr-3 bg-gradient-to-r from-amber-400 to-yellow-500 rounded-full flex items-center justify-center">
															<div className="w-2 h-2 bg-white rounded-full"></div>
														</div>
														<span className="text-amber-700 font-medium">Benefícios VIP</span>
													</DropdownMenuItem>
												)}

												<DropdownMenuItem className="px-4 py-3 cursor-pointer hover:bg-slate-50 transition-colors duration-200">
													<Settings className="mr-3 h-4 w-4 text-slate-500" />
													<span className="text-slate-700">Configurações</span>
												</DropdownMenuItem>
											</div>

											<DropdownMenuSeparator className="my-2 bg-slate-100" />

											<div className="py-2">
												<DropdownMenuItem className="px-4 py-3 cursor-pointer hover:bg-red-50 transition-colors duration-200 text-red-600">
													<LogOut className="mr-3 h-4 w-4" />
													<span>Sair da Conta</span>
												</DropdownMenuItem>
											</div>
										</DropdownMenuContent>
									</DropdownMenu>
								</div>

								{/* Ações compactas para mobile */}
								<div className="flex md:hidden items-center space-x-2">
									{/* Pesquisa mobile */}
									<Button
										variant="ghost"
										className="p-3 hover:bg-slate-100/80 rounded-xl transition-all duration-300"
										onClick={() => setIsMobileSearchOpen(true)}
									>
										<Search className="h-5 w-5 text-slate-600" />
									</Button>

									{/* Carrinho mobile com badge */}
									<Button variant="ghost" className="relative p-3 hover:bg-slate-100/80 rounded-xl transition-all duration-300">
										<ShoppingCart className="h-5 w-5 text-slate-600" />
										<Badge className="absolute -top-1 -right-1 h-4 w-4 p-0 bg-gradient-to-r from-slate-800 to-slate-900 text-white text-xs">
											2
										</Badge>
									</Button>

									{/* Avatar mobile simples */}
									<Button
										variant="ghost"
										className="relative p-2 hover:bg-slate-100/80 rounded-xl transition-all duration-300"
										onClick={() => setIsMobileMenuOpen(true)}
									>
										<div className="relative">
											<Avatar className="h-8 w-8 ring-2 ring-amber-200/50">
												<AvatarImage src={user.avatar} alt={user.name} />
												<AvatarFallback className="bg-gradient-to-br from-slate-800 to-blue-900 text-white font-semibold text-xs">
													{user.name
														.split(" ")
														.map(n => n[0])
														.join("")}
												</AvatarFallback>
											</Avatar>
											{user.isVip && (
												<div className="absolute -bottom-0.5 -right-0.5 bg-gradient-to-r from-amber-400 to-yellow-500 rounded-full p-0.5">
													<div className="w-1.5 h-1.5 bg-white rounded-full"></div>
												</div>
											)}
										</div>
									</Button>
								</div>
							</div>
						</div>
					</div>
				</div>
			</header>

			{/* Navigation Bar */}
			<Navigation />

			{/* Menu Mobile */}
			<MobileMenu
				isOpen={isMobileMenuOpen}
				onToggle={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
				search={search || ""}
				onSearchChange={setSearch}
				user={user}
			/>

			{/* Pesquisa Mobile */}
			<MobileSearch
				isOpen={isMobileSearchOpen}
				onToggle={() => setIsMobileSearchOpen(!isMobileSearchOpen)}
				search={search || ""}
				onSearchChange={setSearch}
			/>
		</>
	);
};
