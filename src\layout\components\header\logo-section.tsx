"use client";
import Link from "next/link";

export const LogoSection = () => {
	return (
		<section className="bg-gradient-to-r from-slate-50/50 via-white to-slate-50/50 border-b border-slate-100/40">
			{/* Subtle gradient accent */}
			<div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-amber-200/15 to-transparent"></div>
			
			<div className="container mx-auto px-4 md:px-6 py-6 md:py-8">
				<div className="flex items-center justify-center">
					<Link href="/" className="group flex items-center space-x-4 md:space-x-6">
						{/* Enhanced Logo Design */}
						<div className="relative">
							{/* Outer glow ring */}
							<div className="absolute inset-0 w-16 h-16 md:w-20 md:h-20 rounded-full bg-gradient-to-br from-amber-50 via-yellow-25 to-amber-100/80 shadow-lg transform group-hover:scale-105 transition-all duration-500 opacity-70"></div>
							
							{/* Main logo container */}
							<div className="relative flex items-center justify-center w-16 h-16 md:w-20 md:h-20 rounded-full bg-gradient-to-br from-slate-900 via-blue-950 to-slate-800 shadow-2xl border border-slate-700/30 group-hover:shadow-3xl transition-all duration-500">
								{/* Inner highlight */}
								<div className="absolute inset-1 rounded-full bg-gradient-to-br from-white/10 to-transparent"></div>
								
								{/* Main K letter */}
								<span
									className="relative z-10 font-serif font-bold text-2xl md:text-3xl text-white tracking-tighter drop-shadow-lg group-hover:scale-110 transition-transform duration-500"
									style={{
										fontFamily: "Playfair Display, Georgia, Times, serif",
										letterSpacing: "-0.05em",
										textShadow: "0 2px 4px rgba(0,0,0,0.4)",
									}}
								>
									K
								</span>
								
								{/* & Co. text */}
								<span
									className="absolute bottom-1 right-1.5 text-sm font-light text-white/95 group-hover:text-white transition-colors duration-500"
									style={{
										fontFamily: "Inter, system-ui, sans-serif",
										letterSpacing: "0.03em",
										fontSize: "0.7rem",
									}}
								>
									&Co.
								</span>
								
								{/* Decorative line */}
								<div className="absolute bottom-1.5 left-1/2 w-4 md:w-5 h-px bg-gradient-to-r from-transparent via-white/40 to-transparent transform -translate-x-1/2 group-hover:via-white/60 transition-colors duration-500"></div>
							</div>
							
							{/* Light accent */}
							<div className="absolute top-3 right-3 w-1.5 h-1.5 rounded-full bg-white/60 blur-sm group-hover:bg-white/80 transition-colors duration-500"></div>
						</div>
						
						{/* Brand text */}
						<div className="text-center space-y-2">
							<h1 className="text-3xl md:text-4xl lg:text-5xl font-light tracking-tight group-hover:scale-105 transition-transform duration-500">
								<span
									className="font-serif bg-gradient-to-r from-slate-800 via-slate-700 to-blue-900 bg-clip-text text-transparent group-hover:from-slate-900 group-hover:to-blue-950 transition-all duration-500"
									style={{
										fontFamily: "Playfair Display, Georgia, Times, serif",
										fontWeight: "400",
									}}
								>
									Kevin
								</span>
								<span
									className="font-extralight text-slate-600 ml-3 group-hover:text-slate-700 transition-colors duration-500"
									style={{
										fontFamily: "Inter, system-ui, sans-serif",
										letterSpacing: "0.02em",
									}}
								>
									& Co.
								</span>
							</h1>
							
							<div className="flex items-center justify-center space-x-3">
								<div className="w-8 md:w-12 h-px bg-gradient-to-r from-amber-400 to-yellow-500 group-hover:from-amber-500 group-hover:to-yellow-600 transition-colors duration-500"></div>
								<p
									className="text-sm md:text-base text-slate-500 font-medium uppercase tracking-widest group-hover:text-slate-600 transition-colors duration-500"
									style={{
										fontFamily: "Inter, system-ui, sans-serif",
										letterSpacing: "0.15em",
									}}
								>
									Premium E-Commerce
								</p>
								<div className="w-8 md:w-12 h-px bg-gradient-to-r from-yellow-500 to-amber-400 group-hover:from-yellow-600 group-hover:to-amber-500 transition-colors duration-500"></div>
							</div>
							
							{/* Tagline */}
							<p className="text-xs md:text-sm text-slate-400 font-light italic group-hover:text-slate-500 transition-colors duration-500">
								Experiência premium em cada detalhe
							</p>
						</div>
					</Link>
				</div>
			</div>
		</section>
	);
};
