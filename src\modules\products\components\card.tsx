import { Button } from "@/shared/components/shadcn/button";
import Link from "next/link";
import { IProduct } from "./product";

interface IProductCardProps {
	product: IProduct;
	className?: string;
}

export default function ProductCard({ product, className = "" }: IProductCardProps) {
	const formatPrice = (price: number) => {
		return new Intl.NumberFormat("pt-BR", {
			style: "currency",
			currency: "BRL",
		}).format(price);
	};

	const renderStars = (rating: number) => {
		const stars = [];
		const fullStars = Math.floor(rating);
		const hasHalfStar = rating % 1 !== 0;

		for (let i = 0; i < fullStars; i++) {
			stars.push(
				<span key={i} className="text-yellow-400">
					★
				</span>
			);
		}

		if (hasHalfStar) {
			stars.push(
				<span key="half" className="text-yellow-400">
					☆
				</span>
			);
		}

		const emptyStars = 5 - Math.ceil(rating);
		for (let i = 0; i < emptyStars; i++)
			stars.push(
				<span key={`empty-${i}`} className="text-gray-300">
					★
				</span>
			);

		return stars;
	};

	const handleAddToCart = (e: React.MouseEvent) => {
		e.preventDefault();
		e.stopPropagation();
		// Mock add to cart functionality
		console.log("Added to cart:", product.name);
	};

	return (
		<div className={`bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden ${className}`}>
			<Link href={`/produto/${product.slug}`}>
				<div className="relative">
					{/* Product image */}
					<div className="aspect-square bg-gray-200 overflow-hidden">
						<img
							src={product.images[0] || "/images/placeholder-product.jpg"}
							alt={product.name}
							className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
						/>
					</div>

					{/* Badges */}
					<div className="absolute top-2 left-2 flex flex-col space-y-1">
						{product.isNew && <span className="bg-green-500 text-white text-xs px-2 py-1 rounded-full font-semibold">Novo</span>}
						{product.discount > 0 && (
							<span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full font-semibold">-{product.discount}%</span>
						)}
						{product.isFeatured && <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full font-semibold">Destaque</span>}
					</div>

					{/* Stock status */}
					{product.stock === 0 && (
						<div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
							<span className="bg-red-600 text-white px-4 py-2 rounded-lg font-semibold">Esgotado</span>
						</div>
					)}
				</div>

				{/* Product info */}
				<div className="p-4">
					{/* Brand */}
					<p className="text-sm text-gray-500 mb-1">{product.brand}</p>

					{/* Name */}
					<h3 className="font-semibold text-gray-900 mb-2 line-clamp-2 hover:text-blue-600 transition-colors">{product.name}</h3>

					{/* Short description */}
					<p className="text-sm text-gray-600 mb-3 line-clamp-2">{product.shortDescription}</p>

					{/* Rating */}
					<div className="flex items-center mb-3">
						<div className="flex items-center">{renderStars(product.rating)}</div>
						<span className="text-sm text-gray-500 ml-2">({product.reviewCount})</span>
					</div>

					{/* Price */}
					<div className="mb-4">
						{product.discount > 0 ? (
							<div className="flex items-center space-x-2">
								<span className="text-lg font-bold text-green-600">{formatPrice(product.price)}</span>
								<span className="text-sm text-gray-500 line-through">{formatPrice(product.originalPrice)}</span>
							</div>
						) : (
							<span className="text-lg font-bold text-gray-900">{formatPrice(product.price)}</span>
						)}
					</div>
				</div>
			</Link>

			{/* Add to cart button */}
			<div className="px-4 pb-4">
				<Button
					onClick={handleAddToCart}
					disabled={product.stock === 0}
					className="w-full"
					variant={product.stock === 0 ? "secondary" : "primary"}
				>
					{product.stock === 0 ? "Esgotado" : "Adicionar ao Carrinho"}
				</Button>
			</div>
		</div>
	);
}
