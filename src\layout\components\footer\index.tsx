import Link from "next/link";

const FOOTER_DETAILS = {
	company: {
		name: "E-Commerce",
		description: "Sua loja online de confiança com os melhores produtos e preços do mercado.",
		links: [
			{ href: "#", label: "Facebook" },
			{ href: "#", label: "Instagram" },
			{ href: "#", label: "Twitter" },
			{ href: "#", label: "LinkedIn" },
		],
	},
	contact: {
		email: "<EMAIL>",
		phone: "(11) 1234-5678",
		chat: "Chat online 24h",
		address: "Rua Exemplo, 123 - São Paulo, SP",
	},
};

export default function Footer() {
	const currentYear = new Date().getFullYear();

	return (
		<footer className="bg-gray-900 text-white">
			<div className="container mx-auto px-4 py-12">
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
					<div>
						<h3 className="text-xl font-bold mb-4">{FOOTER_DETAILS.company.name}</h3>
						<p className="text-gray-300 mb-4">{FOOTER_DETAILS.company.description}</p>
						<div className="flex space-x-4">
							<a href="#" className="text-gray-300 hover:text-white transition-colors">
								📘
							</a>
							<a href="#" className="text-gray-300 hover:text-white transition-colors">
								📷
							</a>
							<a href="#" className="text-gray-300 hover:text-white transition-colors">
								🐦
							</a>
							<a href="#" className="text-gray-300 hover:text-white transition-colors">
								💼
							</a>
						</div>
					</div>
					<div>
						<h4 className="text-lg font-semibold mb-4">Links Rápidos</h4>
						<ul className="space-y-2">
							<li>
								<Link href="/" className="text-gray-300 hover:text-white transition-colors">
									Home
								</Link>
							</li>
							<li>
								<Link href="/categorias" className="text-gray-300 hover:text-white transition-colors">
									Categorias
								</Link>
							</li>
							<li>
								<Link href="/quem-somos" className="text-gray-300 hover:text-white transition-colors">
									Quem Somos
								</Link>
							</li>
							<li>
								<Link href="/suporte" className="text-gray-300 hover:text-white transition-colors">
									Suporte
								</Link>
							</li>
							<li>
								<Link href="/rastreamento" className="text-gray-300 hover:text-white transition-colors">
									Rastreamento
								</Link>
							</li>
						</ul>
					</div>
					<div>
						<h4 className="text-lg font-semibold mb-4">Categorias</h4>
						<ul className="space-y-2">
							<li>
								<Link href="/categoria/eletronicos" className="text-gray-300 hover:text-white transition-colors">
									Eletrônicos
								</Link>
							</li>
							<li>
								<Link href="/categoria/roupas" className="text-gray-300 hover:text-white transition-colors">
									Roupas
								</Link>
							</li>
							<li>
								<Link href="/categoria/esportes" className="text-gray-300 hover:text-white transition-colors">
									Esportes
								</Link>
							</li>
							<li>
								<Link href="/categoria/casa" className="text-gray-300 hover:text-white transition-colors">
									Casa e Decoração
								</Link>
							</li>
							<li>
								<Link href="/categoria/livros" className="text-gray-300 hover:text-white transition-colors">
									Livros
								</Link>
							</li>
						</ul>
					</div>
					<div>
						<h4 className="text-lg font-semibold mb-4">Contato</h4>
						<div className="space-y-2 text-gray-300">
							<p className="flex items-center">
								<span className="mr-2">📧</span>
								{FOOTER_DETAILS.contact.email}
							</p>
							<p className="flex items-center">
								<span className="mr-2">📞</span>
								{FOOTER_DETAILS.contact.phone}
							</p>
							<p className="flex items-center">
								<span className="mr-2">💬</span>
								{FOOTER_DETAILS.contact.chat}
							</p>
							<p className="flex items-center">
								<span className="mr-2">📍</span>
								{FOOTER_DETAILS.contact.address}
							</p>
						</div>
					</div>
				</div>
			</div>
			<div className="border-t border-gray-800">
				<div className="container mx-auto px-4 py-6">
					<div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
						<div>
							<h5 className="text-sm font-semibold mb-2">Formas de Pagamento</h5>
							<div className="flex space-x-2 text-2xl">
								<span>💳</span>
								<span>💰</span>
								<span>🏦</span>
								<span>📱</span>
							</div>
						</div>
						<div>
							<h5 className="text-sm font-semibold mb-2">Segurança</h5>
							<div className="flex space-x-2 text-2xl">
								<span>🔒</span>
								<span>🛡️</span>
								<span>✅</span>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div className="border-t border-gray-800">
				<div className="container mx-auto px-4 py-4">
					<div className="flex flex-col md:flex-row justify-between items-center text-sm text-gray-400">
						<p>© {currentYear} E-Commerce. Todos os direitos reservados.</p>
						<div className="flex space-x-4 mt-2 md:mt-0">
							<Link href="/termos" className="hover:text-white transition-colors">
								Termos de Uso
							</Link>
							<Link href="/privacidade" className="hover:text-white transition-colors">
								Política de Privacidade
							</Link>
							<Link href="/cookies" className="hover:text-white transition-colors">
								Cookies
							</Link>
						</div>
					</div>
				</div>
			</div>
		</footer>
	);
}
