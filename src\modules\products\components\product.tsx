import ProductCard from "./card";

interface IProductGridProps {
	products: IProduct[];
	className?: string;
	emptyMessage?: string;
}

export interface IProduct {
	id: string;
	name: string;
	slug: string;
	description: string;
	shortDescription: string;
	price: number;
	originalPrice: number;
	discount: number;
	category: string;
	brand: string;
	stock: number;
	rating: number;
	reviewCount: number;
	images: string[];
	specifications: IProductSpecifications;
	features: string[];
	isFeatured: boolean;
	isNew: boolean;
	tags: string[];
	sizes?: string[];
	colors?: string[];
}
export interface IProductSpecifications {
	[key: string]: string;
}

export default function ProductGrid({ products, className = "", emptyMessage = "Nenhum produto encontrado." }: IProductGridProps) {
	if (!products.length) {
		return (
			<div className={`text-center py-12 ${className}`}>
				<div className="text-6xl mb-4">📦</div>
				<h3 className="text-xl font-semibold text-gray-900 mb-2">{emptyMessage}</h3>
				<p className="text-gray-600">Tente ajustar os filtros ou explore outras categorias.</p>
			</div>
		);
	}

	return (
		<div className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 ${className}`}>
			{products.map(product => (
				<ProductCard key={product.id} product={product} />
			))}
		</div>
	);
}
