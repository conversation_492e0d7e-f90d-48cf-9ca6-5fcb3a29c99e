import { ReactNode } from "react";
import Footer from "../footer";
import { Header } from "../header/header";

interface MainContainerProps {
	children: ReactNode;
}

export const MainContainer = ({ children }: MainContainerProps) => {
	return (
		<div className="min-h-screen flex flex-col">
			<Header />
			{/* <main className="flex-grow">{children}</main> */}
			<main className="flex-grow container mx-auto px-4 py-6"></main>
			<Footer />
		</div>
	);
};
