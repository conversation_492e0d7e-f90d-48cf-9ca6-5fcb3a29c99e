import bannersData from "@/config/data/banners.json";
import categoriesData from "@/config/data/categories.json";
import productData from "@/config/data/products.json";
import { BannerCarousel } from "../layout/components/banner";

export default function Home() {
	const featuredCategories = categoriesData.filter(category => category.featured);
	const newProducts = productData.filter(product => product.isNew);
	return (
		<div className="space-y-12">
			teste
			<section id="banner">
				<BannerCarousel banners={bannersData} />
			</section>
			<section id="featured-categories" className="container mx-auto px-4">
				<div className="text-center mb-8">
					<h2 className="text-3xl font-bold text-gray-900 mb-4">Explore Nossas Categorias</h2>
					<p className="text-gray-600 max-w-2xl mx-auto">
						Descubra uma ampla variedade de produtos organizados por categoria para facilitar sua busca.
					</p>
				</div>

				<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
					{/* {featuredCategories.map(category => (
						<>Name {category.name}</>
					))} */}
				</div>
			</section>
			<section id="featured-products" className="container mx-auto px-4">
				<div className="text-center mb-8">
					<h2 className="text-3xl font-bold text-gray-900 mb-4">Produtos em Destaque</h2>
					<p className="text-gray-600 max-w-2xl mx-auto">
						Selecionamos especialmente para você os melhores produtos com qualidade garantida.
					</p>
				</div>

				{/* <ProductGrid products={featuredProducts} emptyMessage="Nenhum produto em destaque no momento." /> */}
			</section>
			{newProducts.length > 0 && (
				<section className="bg-gray-50 py-12">
					<div className="container mx-auto px-4">
						<div className="text-center mb-8">
							<h2 className="text-3xl font-bold text-gray-900 mb-4">Novidades</h2>
							<p className="text-gray-600 max-w-2xl mx-auto">Confira os produtos mais recentes que chegaram em nossa loja.</p>
						</div>
						{/* <ProductGrid products={newProducts} emptyMessage="Nenhum produto novo no momento." /> */}
						aqui vai ter os produtos
					</div>
				</section>
			)}
			<section className="bg-blue-600 text-white py-12">
				<div className="container mx-auto px-4 text-center">
					<h2 className="text-3xl font-bold mb-4">Fique por dentro das novidades</h2>
					<p className="text-blue-100 mb-8 max-w-2xl mx-auto">
						Cadastre-se em nossa newsletter e receba ofertas exclusivas, lançamentos e promoções especiais.
					</p>

					<div className="max-w-md mx-auto flex">
						<input
							type="email"
							placeholder="Seu melhor e-mail"
							className="flex-1 px-4 py-3 rounded-l-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-300"
						/>
						<button className="bg-blue-800 hover:bg-blue-900 px-6 py-3 rounded-r-lg font-semibold transition-colors">Cadastrar</button>
					</div>

					<p className="text-xs text-blue-200 mt-4">Não enviamos spam. Você pode cancelar a qualquer momento.</p>
				</div>
			</section>
		</div>
	);
}
